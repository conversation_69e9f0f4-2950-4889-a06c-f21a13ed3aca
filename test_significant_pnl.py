#!/usr/bin/env python3
"""
Test with significant P&L to verify main P&L stat display
"""

import requests
import json

def test_significant_pnl():
    """Test with portfolio that has significant gains to verify P&L display"""
    
    base_url = "http://127.0.0.1:9877"
    
    print("💰 Testing Significant P&L Display")
    print("=" * 40)
    
    session = requests.Session()
    
    # DKK portfolio with significant gains (like +1290 kr)
    portfolio_with_gains = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'AAPL',
                'amount_invested': 10000.0,  # 10000 DKK invested
                'buy_price': 1000.0,         # 1000 DKK per share
                'shares': 10.0,
                'currency': 'DKK',
                'amount_invested_currency': 'DKK',
                'buy_price_currency': 'DKK',
                'purchase_date': '2024-01-15',
                'current_price': 1129.0,     # Current price = 1129 DKK (gain of 129 per share)
                'current_value': 11290.0,    # 10 * 1129 = 11290 DKK
                'pure_gain': 1290.0          # Total gain = 1290 DKK
            }
        ],
        'cash_position': 5000.0,  # 5000 DKK cash
        'detected_currency': 'DKK',
        'currency': 'DKK', 
        'selected_currency': 'DKK',
        'portfolio_currency': 'DKK',
        'total_gain_loss': 1290.0  # Explicit total P&L
    }
    
    try:
        print("📥 Importing portfolio with +1290 kr gain...")
        import_response = session.post(
            f"{base_url}/api/import/confirm",
            json=portfolio_with_gains,
            headers={'Content-Type': 'application/json'}
        )
        
        if import_response.status_code == 200:
            print("✅ Portfolio imported successfully")
            
            print("🔍 Checking P&L display...")
            portfolio_response = session.get(f"{base_url}/portfolio")
            
            if portfolio_response.status_code == 200:
                html_content = portfolio_response.text
                
                # Look for the main P&L stat
                import re
                
                # Look for P&L patterns in the main stats area
                pnl_section_match = re.search(r'<div class="stat-label">P&L</div>\s*<div class="stat-value" id="total-pnl">(.*?)</div>', html_content, re.DOTALL)
                
                if pnl_section_match:
                    pnl_content = pnl_section_match.group(1).strip()
                    print(f"✅ Found P&L section content: {pnl_content}")
                    
                    # Check if it contains DKK format
                    if 'kr' in pnl_content:
                        print("✅ P&L shows DKK currency (kr)")
                        
                        # Look for positive gain pattern
                        if '+' in pnl_content and 'kr' in pnl_content:
                            print("✅ P&L shows positive gain with DKK format")
                        else:
                            print("⚠️  P&L format might need verification")
                    else:
                        print("❌ P&L does not show DKK currency")
                        print(f"   Content: {pnl_content}")
                else:
                    print("❌ Could not find P&L section")
                    
                # Also check for any +1290 kr patterns
                gain_patterns = re.findall(r'\+1290[^<]*kr|\+1,290[^<]*kr', html_content)
                if gain_patterns:
                    print(f"✅ Found specific gain amount: {gain_patterns}")
                    
            else:
                print(f"❌ Failed to get portfolio page: {portfolio_response.status_code}")
                
        else:
            print(f"❌ Failed to import portfolio: {import_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n✅ Significant P&L Test Complete")

if __name__ == "__main__":
    test_significant_pnl()
