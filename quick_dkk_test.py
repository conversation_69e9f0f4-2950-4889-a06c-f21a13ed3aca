#!/usr/bin/env python3
"""
Quick test to import a DKK portfolio and check P&L display
"""

import requests
import json

def test_dkk_portfolio():
    """Import a DKK portfolio and check the P&L display"""
    
    base_url = "http://127.0.0.1:9877"
    
    print("🇩🇰 Testing DKK Portfolio P&L Display")
    print("=" * 40)
    
    # Create session
    session = requests.Session()
    
    # DKK portfolio with some gains/losses
    dkk_portfolio = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'AAPL',
                'amount_invested': 6900.0,  # 6900 DKK
                'buy_price': 1035.0,        # 1035 DKK per share
                'shares': 6.67,
                'currency': 'DKK',
                'amount_invested_currency': 'DKK',
                'buy_price_currency': 'DKK',
                'purchase_date': '2024-01-15',
                'current_price': 1150.0,    # Current price higher = gain
                'current_value': 7670.5     # 6.67 * 1150 = gain of 770.5 DKK
            },
            {
                'ticker': 'GOOGL', 
                'amount_invested': 13800.0, # 13800 DKK
                'buy_price': 690.0,         # 690 DKK per share
                'shares': 20.0,
                'currency': 'DKK',
                'amount_invested_currency': 'DKK', 
                'buy_price_currency': 'DKK',
                'purchase_date': '2024-02-10',
                'current_price': 650.0,     # Current price lower = loss
                'current_value': 13000.0    # 20 * 650 = loss of 800 DKK
            }
        ],
        'cash_position': 3450.0,  # 3450 DKK cash
        'detected_currency': 'DKK',
        'currency': 'DKK',
        'selected_currency': 'DKK',
        'portfolio_currency': 'DKK'
    }
    
    try:
        # Import the DKK portfolio
        print("📥 Importing DKK portfolio...")
        import_response = session.post(
            f"{base_url}/api/import/confirm",
            json=dkk_portfolio,
            headers={'Content-Type': 'application/json'}
        )
        
        if import_response.status_code == 200:
            print("✅ DKK portfolio imported successfully")
            
            # Calculate expected P&L
            total_invested = 6900.0 + 13800.0  # 20700 DKK
            total_current = 7670.5 + 13000.0   # 20670.5 DKK  
            expected_pnl = total_current - total_invested  # -29.5 DKK (small loss)
            
            print(f"📊 Expected P&L: {expected_pnl:.1f} kr")
            
            # Get portfolio page
            print("🔍 Checking portfolio page...")
            portfolio_response = session.get(f"{base_url}/portfolio")
            
            if portfolio_response.status_code == 200:
                html_content = portfolio_response.text
                
                # Look for P&L in the HTML
                if 'id="total-pnl"' in html_content:
                    print("✅ Found P&L element")
                    
                    # Check for DKK currency symbol
                    if ' kr' in html_content:
                        print("✅ Found 'kr' symbol in HTML (DKK format)")
                        
                        # Look for the specific P&L pattern
                        import re
                        # Look for patterns like "-30 kr" or "+187 kr"
                        pnl_pattern = r'[+\-]?\d+(?:,\d{3})*\s+kr'
                        matches = re.findall(pnl_pattern, html_content)
                        
                        if matches:
                            print(f"✅ Found P&L with DKK format: {matches}")
                        else:
                            print("⚠️  Could not find specific P&L amount with DKK format")
                            
                    else:
                        print("❌ DKK symbol 'kr' not found in HTML")
                        
                else:
                    print("❌ P&L element not found")
                    
            else:
                print(f"❌ Failed to get portfolio page: {portfolio_response.status_code}")
                
        else:
            print(f"❌ Failed to import portfolio: {import_response.status_code}")
            print(f"Response: {import_response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n✅ DKK Portfolio Test Complete")

if __name__ == "__main__":
    test_dkk_portfolio()
