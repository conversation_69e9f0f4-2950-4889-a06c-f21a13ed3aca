#!/usr/bin/env python3
"""
Debug script to check portfolio session data
"""

import requests
import json

def debug_portfolio_session():
    """Debug the portfolio session data"""
    
    base_url = "http://127.0.0.1:9877"
    
    print("🔍 Debugging Portfolio Session")
    print("=" * 40)
    
    session = requests.Session()
    
    # First, import a simple portfolio
    portfolio_data = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'AAPL',
                'amount_invested': 6900.0,
                'buy_price': 1035.0,
                'shares': 6.67,
                'currency': 'DKK',
                'amount_invested_currency': 'DKK',
                'buy_price_currency': 'DKK',
                'purchase_date': '2024-01-15'
            }
        ],
        'cash_position': 3450.0,
        'detected_currency': 'DKK',
        'currency': 'DKK',
        'selected_currency': 'DKK',
        'portfolio_currency': 'DKK'
    }
    
    try:
        print("📥 Importing portfolio...")
        import_response = session.post(
            f"{base_url}/api/import/confirm",
            json=portfolio_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Import response status: {import_response.status_code}")
        if import_response.status_code == 200:
            import_result = import_response.json()
            print(f"Import result: {import_result}")
        else:
            print(f"Import failed: {import_response.text}")
            return
            
        # Now check the portfolio page
        print("\n🔍 Checking portfolio page...")
        portfolio_response = session.get(f"{base_url}/portfolio")
        
        if portfolio_response.status_code == 200:
            html_content = portfolio_response.text
            
            # Extract key information
            import re
            
            # Check holdings count
            holdings_match = re.search(r'<div class="stat-value" id="total-holdings">(\d+)</div>', html_content)
            if holdings_match:
                holdings_count = holdings_match.group(1)
                print(f"Holdings count: {holdings_count}")
            else:
                print("❌ Could not find holdings count")
                
            # Check day high/low currency
            day_high_match = re.search(r'<div class="stat-value" id="day-high">([^<]+)</div>', html_content)
            if day_high_match:
                day_high_value = day_high_match.group(1)
                print(f"Day High: {day_high_value}")
            else:
                print("❌ Could not find day high")
                
            # Check P&L
            pnl_match = re.search(r'<div class="stat-value" id="total-pnl">\s*([^<]+?)\s*</div>', html_content, re.DOTALL)
            if pnl_match:
                pnl_value = pnl_match.group(1).strip()
                print(f"P&L: {pnl_value}")
            else:
                print("❌ Could not find P&L")
                
            # Check if portfolio table has data
            if 'data-ticker="AAPL"' in html_content:
                print("✅ Found AAPL in portfolio table")
            else:
                print("❌ AAPL not found in portfolio table")
                
        else:
            print(f"❌ Failed to get portfolio page: {portfolio_response.status_code}")
            
        # Also check the API summary endpoint
        print("\n🔍 Checking API summary...")
        summary_response = session.get(f"{base_url}/api/portfolio-summary")
        
        if summary_response.status_code == 200:
            summary_data = summary_response.json()
            print(f"API Summary: {json.dumps(summary_data, indent=2)}")
        else:
            print(f"❌ Failed to get API summary: {summary_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n✅ Debug Complete")

if __name__ == "__main__":
    debug_portfolio_session()
