#!/usr/bin/env python3
"""
Test script to verify P&L currency display is working correctly
"""

import requests
import json
import time

def test_pnl_currency_display():
    """Test that P&L displays the correct currency symbol based on portfolio settings"""
    
    base_url = "http://127.0.0.1:9877"
    
    print("🧪 Testing P&L Currency Display")
    print("=" * 50)
    
    # Test data for different currencies
    test_scenarios = [
        {
            'name': 'USD Portfolio',
            'currency': 'USD',
            'expected_symbol': '$',
            'portfolio_data': [
                {'ticker': 'AAPL', 'amount_invested': 1000.0, 'buy_price': 150.0, 'shares': 6.67},
                {'ticker': 'GOOGL', 'amount_invested': 2000.0, 'buy_price': 100.0, 'shares': 20.0}
            ]
        },
        {
            'name': 'DKK Portfolio', 
            'currency': 'DKK',
            'expected_symbol': 'kr',
            'portfolio_data': [
                {'ticker': 'AAPL', 'amount_invested': 6900.0, 'buy_price': 1035.0, 'shares': 6.67},
                {'ticker': 'GOOGL', 'amount_invested': 13800.0, 'buy_price': 690.0, 'shares': 20.0}
            ]
        },
        {
            'name': 'EUR Portfolio',
            'currency': 'EUR', 
            'expected_symbol': '€',
            'portfolio_data': [
                {'ticker': 'AAPL', 'amount_invested': 925.0, 'buy_price': 138.75, 'shares': 6.67},
                {'ticker': 'GOOGL', 'amount_invested': 1850.0, 'buy_price': 92.50, 'shares': 20.0}
            ]
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🔍 Testing {scenario['name']}")
        print("-" * 30)
        
        # Create session for this test
        session = requests.Session()
        
        try:
            # Step 1: Import portfolio with specific currency
            print(f"📥 Importing portfolio in {scenario['currency']}")
            
            import_data = {
                'success': True,
                'portfolio': scenario['portfolio_data'],
                'cash_position': 500.0,
                'detected_currency': scenario['currency'],
                'currency': scenario['currency'],
                'selected_currency': scenario['currency'],
                'portfolio_currency': scenario['currency']
            }
            
            # Simulate portfolio import by posting to the correct import endpoint
            import_response = session.post(
                f"{base_url}/api/import/confirm",
                json=import_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if import_response.status_code == 200:
                print(f"✅ Portfolio imported successfully")
            else:
                print(f"❌ Portfolio import failed: {import_response.status_code}")
                continue
            
            # Step 2: Get portfolio page and check P&L display
            print(f"🔍 Checking P&L display for {scenario['currency']}")
            
            portfolio_response = session.get(f"{base_url}/portfolio")
            
            if portfolio_response.status_code == 200:
                html_content = portfolio_response.text
                
                # Look for P&L display in the HTML
                if 'id="total-pnl"' in html_content:
                    print(f"✅ Found P&L element in HTML")
                    
                    # Check if the expected currency symbol is present
                    if scenario['expected_symbol'] in html_content:
                        print(f"✅ Currency symbol '{scenario['expected_symbol']}' found in HTML")
                    else:
                        print(f"❌ Currency symbol '{scenario['expected_symbol']}' NOT found in HTML")
                        
                    # For DKK, check that the format is "amount kr" not "kr amount"
                    if scenario['currency'] in ['DKK', 'SEK', 'NOK']:
                        # Look for pattern like "187 kr" instead of "kr187"
                        import re
                        pnl_pattern = r'[+\-]?\d+(?:,\d{3})*(?:\.\d{2})?\s+' + re.escape(scenario['expected_symbol'])
                        if re.search(pnl_pattern, html_content):
                            print(f"✅ Correct format for {scenario['currency']}: amount followed by symbol")
                        else:
                            print(f"⚠️  Could not verify format for {scenario['currency']} (might be zero P&L)")
                    
                else:
                    print(f"❌ P&L element not found in HTML")
                    
            else:
                print(f"❌ Failed to get portfolio page: {portfolio_response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing {scenario['name']}: {e}")
        
        # Small delay between tests
        time.sleep(1)
    
    print(f"\n✅ P&L Currency Display Test Complete")

if __name__ == "__main__":
    test_pnl_currency_display()
