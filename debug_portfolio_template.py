#!/usr/bin/env python3
"""
Debug script to check portfolio template rendering
"""

import requests
import json
import re

def debug_portfolio_template():
    """Debug the portfolio template rendering"""
    
    base_url = "http://127.0.0.1:9877"
    
    print("🔍 Debugging Portfolio Template Rendering")
    print("=" * 50)
    
    session = requests.Session()
    
    # First, import a simple portfolio
    portfolio_data = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'AAPL',
                'amount_invested': 6900.0,
                'buy_price': 1035.0,
                'shares': 6.67,
                'currency': 'DKK',
                'amount_invested_currency': 'DKK',
                'buy_price_currency': 'DKK',
                'purchase_date': '2024-01-15'
            }
        ],
        'cash_position': 3450.0,
        'detected_currency': 'DKK',
        'currency': 'DKK',
        'selected_currency': 'DKK',
        'portfolio_currency': 'DKK'
    }
    
    try:
        print("📥 Importing portfolio...")
        import_response = session.post(
            f"{base_url}/api/import/confirm",
            json=portfolio_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Import response status: {import_response.status_code}")
        if import_response.status_code == 200:
            import_result = import_response.json()
            print(f"Import result: {import_result}")
        else:
            print(f"Import failed: {import_response.text}")
            return
            
        # Now check the portfolio page
        print("\n🔍 Checking portfolio page...")
        portfolio_response = session.get(f"{base_url}/portfolio")
        
        if portfolio_response.status_code == 200:
            html_content = portfolio_response.text
            
            # Check template variables
            print("\n📊 Template Variables:")
            
            # Check holdings count
            holdings_match = re.search(r'<div class="stat-value" id="total-holdings">([^<]+)</div>', html_content)
            if holdings_match:
                holdings_count = holdings_match.group(1).strip()
                print(f"  Holdings count: '{holdings_count}'")
            else:
                print("  ❌ Could not find holdings count")
                
            # Check day high/low currency
            day_high_match = re.search(r'<div class="stat-value" id="day-high">([^<]+)</div>', html_content)
            if day_high_match:
                day_high_value = day_high_match.group(1).strip()
                print(f"  Day High: '{day_high_value}'")
            else:
                print("  ❌ Could not find day high")
                
            day_low_match = re.search(r'<div class="stat-value" id="day-low">([^<]+)</div>', html_content)
            if day_low_match:
                day_low_value = day_low_match.group(1).strip()
                print(f"  Day Low: '{day_low_value}'")
            else:
                print("  ❌ Could not find day low")
                
            # Check P&L
            pnl_match = re.search(r'<div class="stat-value" id="total-pnl">\s*([^<]+?)\s*</div>', html_content, re.DOTALL)
            if pnl_match:
                pnl_value = pnl_match.group(1).strip()
                print(f"  P&L: '{pnl_value}'")
            else:
                print("  ❌ Could not find P&L")
                
            # Check portfolio table structure
            print("\n📋 Portfolio Table:")
            
            # Look for the table body
            tbody_match = re.search(r'<tbody>(.*?)</tbody>', html_content, re.DOTALL)
            if tbody_match:
                tbody_content = tbody_match.group(1)
                print(f"  Table body found (length: {len(tbody_content)} chars)")
                
                # Count rows
                row_matches = re.findall(r'<tr[^>]*data-ticker="([^"]*)"', tbody_content)
                print(f"  Found {len(row_matches)} rows with data-ticker attributes")
                for ticker in row_matches:
                    print(f"    - {ticker}")
                    
                # Check if AAPL row exists
                if 'data-ticker="AAPL"' in tbody_content:
                    print("  ✅ AAPL row found in table")
                    
                    # Extract AAPL row content
                    aapl_row_match = re.search(r'<tr[^>]*data-ticker="AAPL"[^>]*>(.*?)</tr>', tbody_content, re.DOTALL)
                    if aapl_row_match:
                        aapl_row = aapl_row_match.group(1)
                        print(f"  AAPL row content (first 200 chars): {aapl_row[:200]}...")
                else:
                    print("  ❌ AAPL row not found in table")
                    print(f"  Table body content (first 500 chars): {tbody_content[:500]}...")
            else:
                print("  ❌ Table body not found")
                
            # Check for portfolio loop in template
            if '{% for stock in portfolio %}' in html_content:
                print("  ✅ Portfolio loop found in template")
            else:
                print("  ❌ Portfolio loop not found in template")
                
            # Check for empty portfolio message
            if 'No stocks in portfolio' in html_content or 'portfolio is empty' in html_content.lower():
                print("  ⚠️  Empty portfolio message found")
                
        else:
            print(f"❌ Failed to get portfolio page: {portfolio_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n✅ Debug Complete")

if __name__ == "__main__":
    debug_portfolio_template()
